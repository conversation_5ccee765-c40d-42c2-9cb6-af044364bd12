from agents import Agent, WebSearchTool, Runner
from pydantic import BaseModel
from typing import Optional


# Define the context class for the agent
class CustomerContext(BaseModel):
    user_id: Optional[str] = None
    query: str


# Create a web search tool configured to search your knowledge base
web_search = WebSearchTool(
    # Configure medium context size for balanced results
    search_context_size="medium"
)

# Create the customer support agent
customer_support_agent = Agent[CustomerContext](
    name="Apifon Knowledge Base Assistant",
    instructions="""You are a helpful customer support agent for Apifon.
    Your primary task is to answer customer questions by searching the Apifon knowledge base at https://kb.apifon.com/.

    When responding to users:
    1. Always be polite, professional, and helpful
    2. Use the web search tool to find relevant information from the knowledge base
    3. If the information isn't available in the knowledge base, acknowledge this and suggest contacting human support
    4. Format your responses clearly with markdown when appropriate
    5. Keep responses concise and focused on answering the user's question
    6. If you need to search, always include "site:kb.apifon.com" in your search query to limit results to the knowledge base
    7. When including URLs in your response, always provide clean URLs without UTM parameters or HTML markup
    8. Format URLs as simple markdown links: [Link Text](https://kb.apifon.com/page-url)
    9. NEVER include HTML tags, attributes, or markup in your responses - only use markdown formatting
    10. Ensure URLs are properly formatted without any HTML attributes mixed in
    11. If you find HTML content in search results, convert it to clean markdown before including it in your response
    12. Never include strings like 'target="_blank"' or 'rel="noopener noreferrer"' in your responses

    Remember that you represent Apifon, so maintain a professional tone at all times.
    """,
    model="gpt-4o-mini",  # You can adjust this based on your needs and budget
    tools=[web_search],
)


# Example function to handle customer queries
async def handle_customer_query(user_id: str, query: str) -> str:
    # Create context for this specific query
    context = CustomerContext(user_id=user_id, query=query)

    # Run the agent with the provided context using Runner
    result = await Runner.run(customer_support_agent, query, context=context)

    return result.final_output
