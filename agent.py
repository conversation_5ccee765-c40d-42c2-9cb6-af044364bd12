from agents import Agent, WebSearchTool, Runner
from pydantic import BaseModel
from typing import Optional


# Define the context class for the agent
class CustomerContext(BaseModel):
    user_id: Optional[str] = None
    query: str


# Create a web search tool configured to search your knowledge base
web_search = WebSearchTool(
    # Configure medium context size for balanced results
    search_context_size="medium"
)

# Create the customer support agent
customer_support_agent = Agent[CustomerContext](
    name="Apifon Knowledge Base Assistant",
    instructions="""You are a helpful customer support agent for Apifon.
    Your primary task is to answer customer questions by searching the Apifon knowledge base at https://kb.apifon.com/.

    When responding to users:
    1. Always be polite, professional, and helpful
    2. Use the web search tool to find relevant information from the knowledge base
    3. If the information isn't available in the knowledge base, acknowledge this and suggest contacting human support
    4. Format your responses clearly with markdown when appropriate
    5. Keep responses concise and focused on answering the user's question
    6. If you need to search, always include "site:kb.apifon.com" in your search query to limit results to the knowledge base

    CRITICAL URL FORMATTING RULES - READ CAREFULLY:
    7. When the web search tool returns results with URLs, EXTRACT ONLY THE CLEAN URL
    8. IGNORE any HTML formatting, attributes, or markup in the search results
    9. If a search result shows: <a href="https://kb.apifon.com/article">Title</a> - ONLY use: https://kb.apifon.com/article
    10. If a search result has UTM parameters like ?utm_source=openai - REMOVE THEM
    11. Your final response should ONLY contain clean URLs like: https://kb.apifon.com/article-name
    12. If you want to make it a link, use markdown: [Article Title](https://kb.apifon.com/article-name)
    13. NEVER copy HTML tags, attributes, or markup from search results into your response

    EXAMPLE:
    ✅ CORRECT: For more details, see https://kb.apifon.com/sms-tracking
    ✅ CORRECT: For more details, see the [SMS Tracking Guide](https://kb.apifon.com/sms-tracking) article.
    ❌ WRONG: For more details, see the https://kb.apifon.com/sms-tracking" target="_blank" rel="noopener noreferrer">SMS Tracking Guide

    Remember that you represent Apifon, so maintain a professional tone at all times.
    """,
    model="gpt-4o-mini",  # You can adjust this based on your needs and budget
    tools=[web_search],
)


# Example function to handle customer queries
async def handle_customer_query(user_id: str, query: str) -> str:
    # Create context for this specific query
    context = CustomerContext(user_id=user_id, query=query)

    # Run the agent with the provided context using Runner
    result = await Runner.run(customer_support_agent, query, context=context)

    # Clean up the response to ensure no malformed HTML
    clean_response = clean_response_urls(result.final_output)

    return clean_response


def clean_response_urls(response: str) -> str:
    """Clean up any malformed HTML in the agent response and ensure clean URLs."""

    # Step 1: Handle the specific malformed pattern you're seeing
    # Pattern: https://example.com" target="_blank" rel="noopener noreferrer">Link Text
    import re

    # Find all instances of this pattern and replace with clean markdown
    def replace_malformed_link(match):
        url = match.group(1)
        link_text = match.group(2).strip()

        # Remove UTM parameters from URL
        if "?utm_" in url:
            url = url.split("?utm_")[0]

        # Create clean markdown link
        if link_text:
            return f"[{link_text}]({url})"
        else:
            return url

    # Pattern to match: URL + malformed HTML + link text
    cleaned = re.sub(
        r'(https?://[^\s"]+)"\s+target="_blank"\s+rel="noopener noreferrer">([^.]*?)(?=\s+article|\.|\s*$)',
        replace_malformed_link,
        response,
    )

    # Step 2: Remove any remaining HTML attributes
    cleaned = cleaned.replace('" target="_blank" rel="noopener noreferrer">', "")

    # Step 3: Clean up any remaining UTM parameters
    if "?utm_" in cleaned:
        parts = cleaned.split("?utm_")
        result = parts[0]
        for i in range(1, len(parts)):
            remainder = parts[i]
            space_pos = remainder.find(" ")
            if space_pos != -1:
                result += remainder[space_pos:]
        cleaned = result

    return cleaned
