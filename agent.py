from agents import Agent, WebSearchTool, Runner
from pydantic import BaseModel
from typing import Optional


# Define the context class for the agent
class CustomerContext(BaseModel):
    user_id: Optional[str] = None
    query: str


# Create a web search tool configured to search your knowledge base
web_search = WebSearchTool(
    # Configure medium context size for balanced results
    search_context_size="medium"
)

# Create the customer support agent
customer_support_agent = Agent[CustomerContext](
    name="Apifon Knowledge Base Assistant",
    instructions="""You are a helpful customer support agent for Apifon.
    Your primary task is to answer customer questions by searching the Apifon knowledge base at https://kb.apifon.com/.

    When responding to users:
    1. Always be polite, professional, and helpful
    2. Use the web search tool to find relevant information from the knowledge base
    3. If the information isn't available in the knowledge base, acknowledge this and suggest contacting human support
    4. Format your responses clearly with markdown when appropriate
    5. Keep responses concise and focused on answering the user's question
    6. If you need to search, always include "site:kb.apifon.com" in your search query to limit results to the knowledge base

    CRITICAL URL FORMATTING RULES:
    7. When referencing knowledge base articles, ONLY include the clean URL like: https://kb.apifon.com/article-name
    8. NEVER include any HTML tags, attributes, UTM parameters, or markup in URLs
    9. NEVER include text like 'target="_blank"', 'rel="noopener noreferrer"', or any HTML attributes
    10. If you want to create a link, use this exact format: [Article Title](https://kb.apifon.com/article-name)
    11. Remove all UTM parameters (like ?utm_source=openai) from URLs before including them
    12. When you find a relevant article, extract just the base URL without any tracking parameters

    EXAMPLE OF CORRECT FORMAT:
    ✅ For more details, see the [SMS Tracking Guide](https://kb.apifon.com/sms-tracking) article.
    ❌ For more details, see the https://kb.apifon.com/sms-tracking" target="_blank" rel="noopener noreferrer">SMS Tracking Guide

    Remember that you represent Apifon, so maintain a professional tone at all times.
    """,
    model="gpt-4o-mini",  # You can adjust this based on your needs and budget
    tools=[web_search],
)


# Example function to handle customer queries
async def handle_customer_query(user_id: str, query: str) -> str:
    # Create context for this specific query
    context = CustomerContext(user_id=user_id, query=query)

    # Run the agent with the provided context using Runner
    result = await Runner.run(customer_support_agent, query, context=context)

    # Clean up the response to ensure no malformed HTML
    clean_response = clean_response_urls(result.final_output)

    return clean_response


def clean_response_urls(response: str) -> str:
    """Clean up any malformed HTML in the agent response and ensure clean URLs."""
    import re

    # Remove any HTML attributes that got mixed with URLs
    # Pattern: URL followed by HTML attributes and optional link text
    cleaned = re.sub(
        r'(\(?)(https?://[^\s"]+)(?:\?[^\s"]*)?"\s+target="_blank"\s+rel="noopener noreferrer">([^<\n]*?)(?=\s|$|\.|\,|\!|\?|and\s)',
        lambda m: (
            f"[{m.group(3).strip()}]({m.group(2)})"
            if m.group(3).strip() and m.group(3).strip() != "kb.apifon.com"
            else m.group(2)
        ),
        response,
    )

    # Remove any remaining HTML attributes from URLs
    cleaned = re.sub(
        r'(https?://[^\s"]+)"\s+target="_blank"\s+rel="noopener noreferrer">?',
        r"\1",
        cleaned,
    )

    # Remove UTM parameters from all URLs
    cleaned = re.sub(r"(https?://[^\s?]+)\?utm_[^\s]*", r"\1", cleaned)

    return cleaned
