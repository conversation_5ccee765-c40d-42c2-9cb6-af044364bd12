#!/usr/bin/env python3

import re

def clean_response_urls(response: str) -> str:
    """Clean up any malformed HTML in the agent response and ensure clean URLs."""
    
    # Step 1: Handle URLs with link text - convert to markdown
    # Pattern: https://example.com" target="_blank" rel="noopener noreferrer">Link Text
    cleaned = re.sub(
        r'(https?://[^\s"]+)"\s+target="_blank"\s+rel="noopener noreferrer">([^<\n.]*?)(?=\s*[\.\,\!]|\s+article|\s*$)',
        lambda m: (
            f"[{m.group(2).strip()}]({m.group(1)})"
            if m.group(2).strip()
            else m.group(1)
        ),
        response,
    )
    
    # Step 2: Handle URLs in parentheses with link text
    # Pattern: (https://example.com)" target="_blank" rel="noopener noreferrer">Link Text  
    cleaned = re.sub(
        r'\((https?://[^\s"]+)\)"\s+target="_blank"\s+rel="noopener noreferrer">([^<\n.]*?)(?=\s*[\.\,\!]|\s+article|\s*$)',
        lambda m: (
            f"[{m.group(2).strip()}]({m.group(1)})"
            if m.group(2).strip()
            else m.group(1)
        ),
        cleaned,
    )

    # Step 3: Clean up any remaining malformed HTML attributes (fallback)
    cleaned = re.sub(
        r'(https?://[^\s"]+)"\s+target="_blank"\s+rel="noopener noreferrer">?',
        r"\1",
        cleaned,
    )

    # Step 4: Remove UTM parameters from all URLs
    cleaned = re.sub(r"(https?://[^\s?]+)\?utm_[^\s]*", r"\1", cleaned)

    return cleaned

# Test with your example
test_input = '''For more detailed instructions, please refer to the https://kb.apifon.com/create-email-campaign" target="_blank" rel="noopener noreferrer">Create Email Campaign article.'''

print("Input:")
print(test_input)
print("\nOutput:")
print(clean_response_urls(test_input))

# Test with multiple URLs
test_input2 = '''For more detailed information, you can refer to the https://kb.apifon.com/sms-tracking" target="_blank" rel="noopener noreferrer">SMS Tracking and https://kb.apifon.com/export-sms-tracking-report" target="_blank" rel="noopener noreferrer">Export SMS Tracking Report articles in our knowledge base.'''

print("\n" + "="*50)
print("Input 2:")
print(test_input2)
print("\nOutput 2:")
print(clean_response_urls(test_input2))
