<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Apifon Knowledge Base Assistant</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap"
      rel="stylesheet"
    />
    <style>
      :root {
        --primary-color: #4361ee;
        --secondary-color: #3f37c9;
        --user-msg-color: #e9ecef;
        --bot-msg-color: #f8f9fa;
        --border-radius: 12px;
        --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      body {
        font-family: "Poppins", Arial, sans-serif;
        max-width: 900px;
        margin: 0 auto;
        padding: 30px;
        background-color: #f5f7fb;
        color: #333;
        line-height: 1.6;
      }

      h1 {
        color: var(--primary-color);
        text-align: center;
        margin-bottom: 10px;
        font-weight: 500;
      }

      p {
        text-align: center;
        color: #666;
        margin-bottom: 30px;
      }

      .chat-container {
        border: 1px solid #e0e0e0;
        border-radius: var(--border-radius);
        padding: 20px;
        height: 500px;
        overflow-y: auto;
        margin-bottom: 20px;
        background-color: white;
        box-shadow: var(--box-shadow);
      }

      .message {
        margin-bottom: 15px;
        padding: 12px 16px;
        border-radius: var(--border-radius);
        max-width: 80%;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        line-height: 1.5;
      }

      .user-message {
        background-color: var(--primary-color);
        color: white;
        text-align: right;
        margin-left: auto;
        border-bottom-right-radius: 4px;
      }

      .bot-message {
        background-color: var(--bot-msg-color);
        border-bottom-left-radius: 4px;
        border-left: 4px solid var(--primary-color);
      }

      .input-container {
        display: flex;
        box-shadow: var(--box-shadow);
        border-radius: var(--border-radius);
        overflow: hidden;
      }

      #user-input {
        flex-grow: 1;
        padding: 14px;
        border: 1px solid #e0e0e0;
        border-radius: var(--border-radius) 0 0 var(--border-radius);
        font-size: 16px;
        outline: none;
        transition: border-color 0.3s;
      }

      #user-input:focus {
        border-color: var(--primary-color);
      }

      button {
        padding: 14px 24px;
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 0 var(--border-radius) var(--border-radius) 0;
        cursor: pointer;
        font-size: 16px;
        font-weight: 500;
        transition: background-color 0.3s;
      }

      button:hover {
        background-color: var(--secondary-color);
      }

      button:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
      }

      .loading {
        display: none;
        margin-top: 10px;
        color: #666;
        text-align: center;
      }

      /* Typing indicator styles */
      .typing-indicator {
        display: none;
        background-color: #f1f1f1;
        border-radius: var(--border-radius);
        padding: 12px 16px;
        margin-bottom: 15px;
        width: fit-content;
        border-bottom-left-radius: 4px;
        border-left: 4px solid var(--primary-color);
      }

      .typing-indicator span {
        height: 8px;
        width: 8px;
        float: left;
        margin: 0 2px;
        background-color: #9e9ea1;
        display: block;
        border-radius: 50%;
        opacity: 0.4;
      }

      .typing-indicator span:nth-of-type(1) {
        animation: 1s blink infinite 0.3333s;
      }

      .typing-indicator span:nth-of-type(2) {
        animation: 1s blink infinite 0.6666s;
      }

      .typing-indicator span:nth-of-type(3) {
        animation: 1s blink infinite 0.9999s;
      }

      @keyframes blink {
        50% {
          opacity: 1;
        }
      }

      /* Code formatting */
      code {
        background-color: #f0f0f0;
        padding: 2px 4px;
        border-radius: 4px;
        font-family: monospace;
        font-size: 0.9em;
      }

      /* Responsive design */
      @media (max-width: 768px) {
        body {
          padding: 15px;
        }

        .message {
          max-width: 90%;
        }
      }

      /* Link styling */
      a {
        color: #3366cc;
        text-decoration: none;
        border-bottom: 1px dotted #3366cc;
        transition: color 0.2s, border-bottom 0.2s;
        word-break: break-word; /* Allow URLs to break at any point */
        max-width: 100%;
        display: inline-block;
      }

      a:hover {
        color: #1a3c80;
        border-bottom: 1px solid #1a3c80;
      }

      .user-message a {
        color: #e0e0e0;
        border-bottom: 1px dotted #e0e0e0;
      }

      .user-message a:hover {
        color: #ffffff;
        border-bottom: 1px solid #ffffff;
      }
    </style>
  </head>
  <body>
    <h1>Apifon Knowledge Base Assistant</h1>
    <p>Ask any question about Apifon services and products</p>

    <div class="chat-container" id="chat-container">
      <div class="message bot-message">
        Hello! I'm the Apifon Knowledge Base Assistant. How can I help you
        today?
      </div>
    </div>

    <div class="input-container">
      <input
        type="text"
        id="user-input"
        placeholder="Type your question here..."
      />
      <button onclick="sendMessage()" id="send-button">Send</button>
    </div>

    <div class="loading" id="loading">Processing your request...</div>

    <script>
      // Allow Enter key to send messages
      document
        .getElementById("user-input")
        .addEventListener("keyup", function (event) {
          if (event.key === "Enter") {
            sendMessage();
          }
        });

      function sendMessage() {
        const userInput = document.getElementById("user-input").value.trim();
        if (!userInput) return;

        // Disable input and button while processing
        document.getElementById("user-input").disabled = true;
        document.getElementById("send-button").disabled = true;

        // Add user message to chat
        addMessage(userInput, "user");

        // Clear input field
        document.getElementById("user-input").value = "";

        // Show typing indicator
        showTypingIndicator();

        // Send request to server
        const formData = new FormData();
        formData.append("user_input", userInput);

        fetch("/chat", {
          method: "POST",
          body: formData,
        })
          .then((response) => response.json())
          .then((data) => {
            // Hide typing indicator
            hideTypingIndicator();

            if (data.error) {
              addMessage("Sorry, an error occurred: " + data.error, "bot");
            } else {
              addMessage(data.response, "bot");
            }

            // Re-enable input and button
            document.getElementById("user-input").disabled = false;
            document.getElementById("send-button").disabled = false;
            document.getElementById("user-input").focus();
          })
          .catch((error) => {
            // Hide typing indicator
            hideTypingIndicator();

            addMessage(
              "Sorry, an error occurred while processing your request.",
              "bot"
            );
            console.error("Error:", error);

            // Re-enable input and button
            document.getElementById("user-input").disabled = false;
            document.getElementById("send-button").disabled = false;
            document.getElementById("user-input").focus();
          });
      }

      function showTypingIndicator() {
        const chatContainer = document.getElementById("chat-container");

        // Create typing indicator
        const typingDiv = document.createElement("div");
        typingDiv.id = "typing-indicator";
        typingDiv.className = "typing-indicator";
        typingDiv.innerHTML = "<span></span><span></span><span></span>";

        // Add to chat and display
        chatContainer.appendChild(typingDiv);
        typingDiv.style.display = "block";

        // Scroll to bottom
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }

      function hideTypingIndicator() {
        const typingIndicator = document.getElementById("typing-indicator");
        if (typingIndicator) {
          typingIndicator.remove();
        }
      }

      function addMessage(text, sender) {
        const chatContainer = document.getElementById("chat-container");
        const messageDiv = document.createElement("div");
        messageDiv.classList.add("message");
        messageDiv.classList.add(
          sender === "user" ? "user-message" : "bot-message"
        );

        // Convert markdown to HTML for bot messages
        if (sender === "bot") {
          // Clean up any malformed HTML first - comprehensive cleanup for all patterns

          // Pattern 1: Handle URLs in parentheses with malformed HTML
          // Example: (https://example.com?utm_source=openai)" target="_blank" rel="noopener noreferrer">kb.apifon.com
          text = text.replace(
            /\((https?:\/\/[^\s")]+(?:\?[^\s")]*)?)\)"\s+target="_blank"\s+rel="noopener noreferrer">([^<\n]*?)(?=\s|$|\.|\,|\!|\?)/g,
            function (match, url, linkText) {
              // Clean UTM parameters from URL
              const cleanUrl = url
                .replace(/[?&]utm_[^&]*/g, "")
                .replace(/[?&]$/, "");
              let cleanLinkText = linkText.trim();

              // If link text is just the domain, create a more descriptive text
              if (
                cleanLinkText === "kb.apifon.com" ||
                cleanLinkText.includes("kb.apifon.com")
              ) {
                // Extract page name from URL for better link text
                const pathMatch = cleanUrl.match(/\/([^/?]+)(?:\?|$)/);
                if (pathMatch) {
                  cleanLinkText = pathMatch[1]
                    .replace(/-/g, " ")
                    .replace(/\b\w/g, (l) => l.toUpperCase());
                }
              }

              return `[${cleanLinkText}](${cleanUrl})`;
            }
          );

          // Pattern 2: Handle regular malformed HTML without parentheses
          // Example: https://example.com" target="_blank" rel="noopener noreferrer">Link Text
          text = text.replace(
            /(https?:\/\/[^\s"]+)"\s+target="_blank"\s+rel="noopener noreferrer">([^<\n]*?)(?=\s|$|\.|\,|\!|\?|and\s|in\s|articles?)/g,
            function (match, url, linkText) {
              // Clean UTM parameters from URL
              const cleanUrl = url
                .replace(/[?&]utm_[^&]*/g, "")
                .replace(/[?&]$/, "");
              let cleanLinkText = linkText.trim();

              // Remove common trailing words that might get captured
              cleanLinkText = cleanLinkText.replace(
                /\s+(articles?|in our knowledge base)$/i,
                ""
              );

              // Create a proper markdown link if there's meaningful link text
              if (cleanLinkText && cleanLinkText.length > 0) {
                return `[${cleanLinkText}](${cleanUrl})`;
              } else {
                return cleanUrl;
              }
            }
          );

          // Fallback cleanup for any remaining malformed patterns
          text = text.replace(
            /(https?:\/\/[^\s"]+)"\s+target="_blank"\s+rel="noopener noreferrer">/g,
            function (match, url) {
              // Clean UTM parameters from URL
              const cleanUrl = url
                .replace(/[?&]utm_[^&]*/g, "")
                .replace(/[?&]$/, "");
              return cleanUrl;
            }
          );

          // Check if text already contains HTML links to avoid double processing
          const hasHtmlLinks =
            /<a\s+[^>]*href\s*=\s*["'][^"']*["'][^>]*>/i.test(text);

          if (!hasHtmlLinks) {
            // URL detection and conversion to links
            // Improved regex that handles complex URLs with special characters
            const urlRegex =
              /(https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&//=]*))/g;

            // Handle markdown links [text](url)
            text = text.replace(
              /\[(.*?)\]\((https?:\/\/[^\s]+)\)/g,
              '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>'
            );

            // Convert plain URLs to clickable links (but avoid URLs that are already in HTML links)
            text = text.replace(urlRegex, function (url) {
              // Check if this URL is already inside an HTML link
              const beforeUrl = text.substring(0, text.indexOf(url));
              const afterUrl = text.substring(text.indexOf(url) + url.length);

              // Simple check to see if we're inside an href attribute
              const lastHref = beforeUrl.lastIndexOf('href="');
              const lastClosingQuote = beforeUrl.lastIndexOf('"');
              const nextClosingQuote = afterUrl.indexOf('"');

              if (lastHref > lastClosingQuote && nextClosingQuote > -1) {
                return url; // Don't modify URLs that are already in href attributes
              }

              const displayUrl = url;
              const href = url.startsWith("www.") ? "https://" + url : url;
              return (
                '<a href="' +
                href +
                '" target="_blank" rel="noopener noreferrer">' +
                displayUrl +
                "</a>"
              );
            });
          }

          // Simple markdown parsing for bold, italic, and code
          text = text.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");
          text = text.replace(/\*(.*?)\*/g, "<em>$1</em>");
          text = text.replace(/`(.*?)`/g, "<code>$1</code>");

          // Handle line breaks
          text = text.replace(/\n/g, "<br>");
        }

        messageDiv.innerHTML = text;
        chatContainer.appendChild(messageDiv);

        // Scroll to bottom
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }
    </script>
  </body>
</html>
